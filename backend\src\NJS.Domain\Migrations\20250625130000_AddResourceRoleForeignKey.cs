using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NJS.Domain.Migrations
{
    /// <inheritdoc />
    public partial class AddResourceRoleForeignKey : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add foreign key constraint from UserWBSTasks.ResourceRoleId to AspNetRoles.Id
            migrationBuilder.CreateIndex(
                name: "IX_UserWBSTasks_ResourceRoleId",
                table: "UserWBSTasks",
                column: "ResourceRoleId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserWBSTasks_AspNetRoles_ResourceRoleId",
                table: "UserWBSTasks",
                column: "ResourceRoleId",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove foreign key constraint
            migrationBuilder.DropForeignKey(
                name: "FK_UserWBSTasks_AspNetRoles_ResourceRoleId",
                table: "UserWBSTasks");

            migrationBuilder.DropIndex(
                name: "IX_UserWBSTasks_ResourceRoleId",
                table: "UserWBSTasks");
        }
    }
}
