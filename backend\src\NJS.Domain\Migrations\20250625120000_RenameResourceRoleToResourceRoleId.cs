using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NJS.Domain.Migrations
{
    /// <inheritdoc />
    public partial class RenameResourceRoleToResourceRoleId : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Rename the ResourceRole column to ResourceRoleId in UserWBSTasks table
            migrationBuilder.RenameColumn(
                name: "ResourceRole",
                table: "UserWBSTasks",
                newName: "ResourceRoleId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Rename the ResourceRoleId column back to ResourceRole in UserWBSTasks table
            migrationBuilder.RenameColumn(
                name: "ResourceRoleId",
                table: "UserWBSTasks",
                newName: "ResourceRole");
        }
    }
}
